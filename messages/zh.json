{"HomePage": {"badge": {"text": "介绍我们的新组件", "action": "了解更多"}, "title": "使用精美组件更快构建", "description": "使用 React 和 Tailwind CSS 构建的高级 UI 组件。节省时间，使用我们现成的组件更快地发布您的下一个项目。", "actions": {"getStarted": "开始使用", "github": "GitHub"}, "image": {"alt": "UI 组件预览"}}, "Navigation": {"home": "首页", "docs": "文档", "components": "组件", "examples": "示例", "pricing": "定价", "about": "关于", "contact": "联系", "signIn": "登录", "signUp": "注册", "profile": "个人资料", "settings": "设置", "logout": "退出"}, "SiteHeader": {"logo": {"title": "ShipSaaS.net", "alt": "shadcn/ui 组件库"}, "menu": {"home": "首页", "products": "产品", "resources": "资源", "pricing": "定价", "blog": "博客", "dashboard": "仪表板", "company": "公司", "careers": "招聘", "support": "支持", "helpCenter": "帮助中心", "contactUs": "联系我们", "status": "状态", "termsOfService": "服务条款"}, "menuDescriptions": {"blog": "最新的行业新闻、更新和信息", "company": "我们的使命是创新并赋能世界", "careers": "浏览职位列表并了解我们的工作环境", "support": "联系我们的支持团队或访问我们的社区论坛", "helpCenter": "在这里获得您需要的所有答案", "contactUs": "我们在这里帮助您解决任何问题", "status": "检查我们服务和API的当前状态", "termsOfService": "使用我们服务的条款和条件"}, "mobileExtraLinks": {"press": "新闻", "contact": "联系", "imprint": "版权信息", "sitemap": "网站地图"}, "auth": {"login": "登录", "signup": "注册"}}, "Common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "edit": "编辑", "delete": "删除", "close": "关闭", "back": "返回", "next": "下一页", "previous": "上一页", "search": "搜索", "filter": "筛选", "sort": "排序", "language": "语言", "theme": "主题", "lightMode": "浅色模式", "darkMode": "深色模式", "systemMode": "系统模式"}, "Dashboard": {"title": "仪表板", "subtitle": "概览和分析", "welcome": "欢迎回来", "navigation": {"dashboard": "仪表板", "profile": "个人资料", "billing": "账单", "security": "安全", "notifications": "通知", "settings": "设置"}, "profile": {"title": "个人资料", "subtitle": "管理您的账户信息", "avatar": {"title": "头像", "description": "点击上传按钮上传自定义头像", "upload": "上传头像", "recommendation": "头像是可选的，但强烈建议设置"}, "name": {"title": "姓名", "description": "请输入您的显示名称", "placeholder": "输入您的姓名", "validation": "请使用3-30个字符作为您的姓名"}, "account": {"title": "账户信息", "description": "您的账户详情和设置", "email": "邮箱地址", "emailNote": "邮箱地址无法更改", "userId": "用户ID", "userIdNote": "您的唯一用户标识符"}}, "billing": {"title": "账单", "subtitle": "管理账单和订阅", "currentPlan": "当前计划", "paymentMethod": "支付方式", "billingHistory": "账单历史", "usageAlerts": "使用警告"}, "security": {"title": "安全", "subtitle": "安全设置和身份验证", "password": "密码和身份验证", "settings": "安全设置", "sessions": "活动会话", "recommendations": "安全建议"}, "notifications": {"title": "通知", "subtitle": "通知偏好设置", "email": "邮件通知", "push": "推送通知", "categories": "通知类别"}, "settings": {"title": "设置", "subtitle": "常规应用程序设置", "appearance": "外观", "language": "语言和地区", "performance": "性能", "privacy": "数据和隐私"}}, "Features": {"header": {"badge": "SaaS 解决方案。真实成果。", "title": "什么是 ShipSaaS？", "subtitle": "完整的 SaaS 开发平台"}, "items": {"rapidDevelopment": {"title": "快速 SaaS 开发", "description": "ShipSaaS 提供了构建和启动 SaaS 应用程序的完整工具包。从身份验证到支付，一切都已预构建并可立即使用。"}, "scalableArchitecture": {"title": "可扩展架构", "description": "采用现代技术和最佳实践构建，ShipSaaS 确保您的应用程序能够从初创公司扩展到企业级别，充满信心。"}, "productionReady": {"title": "生产就绪功能", "description": "开箱即用的基本 SaaS 功能：用户管理、计费、分析等。专注于您的独特价值主张，而非基础设施。"}}}, "WhyChooseShipSaaS": {"header": {"badge": "为什么选择我们。经验证的成功。", "title": "为什么选择 ShipSaaS？", "subtitle": "现代 SaaS 开发的明智选择"}, "items": {"timeToMarket": {"title": "更快的上市时间", "description": "使用我们预构建的组件和模板，让您的 SaaS 产品上市速度提升 10 倍。跳过数月的开发时间，专注于让您的产品独一无二的特性。"}, "costEffective": {"title": "成本效益解决方案", "description": "通过我们的综合启动套件节省数千元的开发成本。以非企业级的价格获得企业级功能。"}, "provenReliability": {"title": "经过验证且可靠", "description": "基于成功 SaaS 公司使用的经过实战检验的技术和模式构建。减少技术债务，确保长期可维护性。"}}}, "Pricing": {"header": {"tag": "简单定价", "title": "选择您的 ShipSaaS 计划", "description": "使用我们的综合开发工具包更快地开始构建您的 SaaS"}, "tiers": {"starter": {"name": "基础版", "price": "99", "description": "适合独立开发者和小型项目", "features": ["Next.js 框架", "TailwindCSS 样式", "国际化支持 (i18n)", "Neon 数据库存储", "主题切换 (深色/浅色)", "Google & GitHub 一键登录", "SEO 友好支持", "Stripe 支付集成", "邮件密码重置功能"]}, "professional": {"name": "标准版", "price": "169", "description": "适合成长中的团队和严肃的 SaaS 项目", "popular": true, "features": ["包含基础版所有功能", "文档系统", "博客功能", "MagicUI 组件库", "TailArk UI 库", "Animate-UI 动画", "Vercel AI SDK", "ChatGPT 集成", "高级 UI 组件"]}, "enterprise": {"name": "高级版", "price": "299", "description": "适合大型团队和企业应用", "features": ["包含标准版所有功能", "用户控制台", "后台管理系统", "积分管理系统", "优先技术支持", "自定义集成", "高级分析", "一对一咨询"]}}}, "FAQs": {"header": {"title": "常见问题", "description": "快速了解关于 ShipSaaS 平台、服务和功能的常见问题的全面答案。"}, "items": [{"id": "item-1", "question": "ShipSaaS 包含什么内容？", "answer": "ShipSaaS 包含完整的 Next.js 模板，具有身份验证、支付、数据库集成、UI 组件、国际化等功能。您快速启动 SaaS 所需的一切。"}, {"id": "item-2", "question": "我可以多快启动我的 SaaS？", "answer": "使用 ShipSaaS，您可以在几分钟内（而不是几个月）拥有一个功能齐全的 SaaS 应用程序。我们预构建的组件和集成消除了数周的开发时间。"}, {"id": "item-3", "question": "你们提供技术支持吗？", "answer": "是的！所有计划都包括文档和社区支持。标准版和高级版计划包括优先技术支持，响应时间更快。"}, {"id": "item-4", "question": "我可以自定义组件和设计吗？", "answer": "当然可以！ShipSaaS 使用 TailwindCSS 构建了现代化、可定制的组件。您可以完全控制样式、品牌和功能，以匹配您的愿景。"}, {"id": "item-5", "question": "ShipSaaS 使用什么技术？", "answer": "ShipSaaS 使用 Next.js 15、React、TypeScript、TailwindCSS、Neon 数据库、NextAuth.js、Stripe 和许多其他现代技术构建，遵循行业最佳实践。"}, {"id": "item-6", "question": "有退款政策吗？", "answer": "我们提供 30 天退款保证。如果您对 ShipSaaS 不满意，请在 30 天内联系我们的支持团队获得全额退款。"}], "contact": {"text": "找不到您要找的内容？联系我们的", "linkText": "客户支持团队"}}, "Footer": {"description": "使用我们的高级组件构建您的下一个项目。", "links": {"product": "产品", "company": "公司", "resources": "资源", "legal": "法律"}, "copyright": "© 2025 Next.js 模板。保留所有权利。"}}