{"HomePage": {"badge": {"text": "Introducing our new components", "action": "Learn more"}, "title": "Build faster with beautiful components", "description": "Premium UI components built with React and Tailwind CSS. Save time and ship your next project faster with our ready-to-use components.", "actions": {"getStarted": "Get Started", "github": "GitHub"}, "image": {"alt": "UI Components Preview"}}, "Navigation": {"home": "Home", "docs": "Documentation", "components": "Components", "examples": "Examples", "pricing": "Pricing", "about": "About", "contact": "Contact", "signIn": "Sign In", "signUp": "Sign Up", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "SiteHeader": {"logo": {"title": "ShipSaaS.net", "alt": "blocks for shadcn/ui"}, "menu": {"home": "Home", "products": "Products", "resources": "Resources", "pricing": "Pricing", "blog": "Blog", "dashboard": "Dashboard", "company": "Company", "careers": "Careers", "support": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us", "status": "Status", "termsOfService": "Terms of Service"}, "menuDescriptions": {"blog": "The latest industry news, updates, and info", "company": "Our mission is to innovate and empower the world", "careers": "Browse job listing and discover our workspace", "support": "Get in touch with our support team or visit our community forums", "helpCenter": "Get all the answers you need right here", "contactUs": "We are here to help you with any questions you have", "status": "Check the current status of our services and APIs", "termsOfService": "Our terms and conditions for using our services"}, "mobileExtraLinks": {"press": "Press", "contact": "Contact", "imprint": "Imprint", "sitemap": "Sitemap"}, "auth": {"login": "Log in", "signup": "Sign up"}}, "Common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "language": "Language", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemMode": "System Mode"}, "Dashboard": {"title": "Dashboard", "subtitle": "Overview and analytics", "welcome": "Welcome back", "navigation": {"dashboard": "Dashboard", "profile": "Profile", "billing": "Billing", "security": "Security", "notifications": "Notifications", "settings": "Settings"}, "profile": {"title": "Profile", "subtitle": "Manage your account information", "avatar": {"title": "Avatar", "description": "Click upload button to upload a custom one", "upload": "Upload Avatar", "recommendation": "An avatar is optional but strongly recommended"}, "name": {"title": "Name", "description": "Please enter your display name", "placeholder": "Enter your name", "validation": "Please use 3-30 characters for your name"}, "account": {"title": "Account Information", "description": "Your account details and settings", "email": "Email Address", "emailNote": "Email address cannot be changed", "userId": "User ID", "userIdNote": "Your unique user identifier"}}, "billing": {"title": "Billing", "subtitle": "Manage billing and subscriptions", "currentPlan": "Current Plan", "paymentMethod": "Payment Method", "billingHistory": "Billing History", "usageAlerts": "<PERSON><PERSON>"}, "security": {"title": "Security", "subtitle": "Security settings and authentication", "password": "Password & Authentication", "settings": "Security Settings", "sessions": "Active Sessions", "recommendations": "Security Recommendations"}, "notifications": {"title": "Notifications", "subtitle": "Notification preferences", "email": "Email Notifications", "push": "Push Notifications", "categories": "Notification Categories"}, "settings": {"title": "Settings", "subtitle": "General application settings", "appearance": "Appearance", "language": "Language & Region", "performance": "Performance", "privacy": "Data & Privacy"}}, "Features": {"header": {"badge": "SaaS Solutions. Real Results.", "title": "What is ShipSaaS?", "subtitle": "The Complete SaaS Development Platform"}, "items": {"rapidDevelopment": {"title": "Rapid SaaS Development", "description": "ShipSaaS provides a complete toolkit for building and launching SaaS applications quickly. From authentication to payments, everything is pre-built and ready to use."}, "scalableArchitecture": {"title": "Scalable Architecture", "description": "Built with modern technologies and best practices, ShipSaaS ensures your application can scale from startup to enterprise level with confidence."}, "productionReady": {"title": "Production-Ready Features", "description": "Get essential SaaS features out of the box: user management, billing, analytics, and more. Focus on your unique value proposition, not infrastructure."}}}, "WhyChooseShipSaaS": {"header": {"badge": "Why Choose Us. Proven Success.", "title": "Why Choose ShipSaaS?", "subtitle": "The Smart Choice for Modern SaaS Development"}, "items": {"timeToMarket": {"title": "Faster Time to Market", "description": "Launch your SaaS product 10x faster with our pre-built components and templates. Skip months of development and focus on what makes your product unique."}, "costEffective": {"title": "Cost-Effective Solution", "description": "Save thousands in development costs with our comprehensive starter kit. Get enterprise-grade features without the enterprise-grade price tag."}, "provenReliability": {"title": "Proven & Reliable", "description": "Built on battle-tested technologies and patterns used by successful SaaS companies. Reduce technical debt and ensure long-term maintainability."}}}, "Pricing": {"header": {"tag": "Simple Pricing", "title": "Choose Your ShipSaaS Plan", "description": "Start building your SaaS faster with our comprehensive development toolkit"}, "tiers": {"starter": {"name": "Basic", "price": "99", "description": "Perfect for indie developers and small projects", "features": ["Next.js Framework", "TailwindCSS Styling", "Internationalization (i18n)", "Neon Database Storage", "Theme Switching (Dark/Light)", "Google & GitHub OAuth Login", "SEO-Friendly Support", "Stripe Payment Integration", "Email Password Reset"]}, "professional": {"name": "Standard", "price": "169", "description": "For growing teams and serious SaaS projects", "popular": true, "features": ["Everything in Basic", "Documentation System", "Blog Functionality", "MagicUI Components", "TailArk UI Library", "Animate-UI Animations", "Vercel AI SDK", "ChatGPT Integration", "Advanced UI Components"]}, "enterprise": {"name": "Premium", "price": "299", "description": "For large teams and enterprise applications", "features": ["Everything in Standard", "User Dashboard Console", "Admin Management System", "Points/Credits Management", "Priority Technical Support", "Custom Integrations", "Advanced Analytics", "1-on-1 Consultation"]}}}, "FAQs": {"header": {"title": "Frequently Asked Questions", "description": "Discover quick and comprehensive answers to common questions about ShipSaaS platform, services, and features."}, "items": [{"id": "item-1", "question": "What is included in ShipSaaS?", "answer": "ShipSaaS includes a complete Next.js template with authentication, payments, database integration, UI components, internationalization, and much more. Everything you need to launch your SaaS quickly."}, {"id": "item-2", "question": "How quickly can I launch my SaaS?", "answer": "With ShipSaaS, you can have a fully functional SaaS application running in minutes, not months. Our pre-built components and integrations eliminate weeks of development time."}, {"id": "item-3", "question": "Do you provide technical support?", "answer": "Yes! All plans include documentation and community support. Standard and Premium plans include priority technical support with faster response times."}, {"id": "item-4", "question": "Can I customize the components and design?", "answer": "Absolutely! ShipSaaS is built with modern, customizable components using TailwindCSS. You have full control over styling, branding, and functionality to match your vision."}, {"id": "item-5", "question": "What technologies does ShipSaaS use?", "answer": "ShipSaaS is built with Next.js 15, React, TypeScript, TailwindCSS, Neon Database, NextAuth.js, Stripe, and many other modern technologies following industry best practices."}, {"id": "item-6", "question": "Is there a refund policy?", "answer": "We offer a 30-day money-back guarantee. If you're not satisfied with ShipSaaS, contact our support team within 30 days for a full refund."}], "contact": {"text": "Can't find what you're looking for? Contact our", "linkText": "customer support team"}}, "Footer": {"description": "Build your next project with our premium components.", "links": {"product": "Product", "company": "Company", "resources": "Resources", "legal": "Legal"}, "copyright": "© 2025 Next.js Template. All rights reserved."}}